﻿using Microsoft.EntityFrameworkCore;
using Models.Models;
using System.Data;
using WinFormsApp1.Constants;

namespace WinFormsApp1
{
    public partial class frmDeTai : BaseForm
    {
        private TaiKhoan currentUser;
        private List<DeTai> allDeTai = new List<DeTai>();

        public frmDeTai(TaiKhoan user)
        {
            currentUser = user;
            InitializeComponent();
            SetupDefaultValues();
            SetupDataGridView();
            SetupUserPermissions();
            LoadFilterData();
            LoadData();
        }

        private void SetupDefaultValues()
        {
            // Thiết lập giá trị mặc định cho DateTimePicker để hiển thị tất cả dữ liệu
            dtpTuNgay.Value = DateTime.Now.AddYears(-5);
            dtpDenNgay.Value = DateTime.Now.AddYears(5);
        }

        private void SetupUserPermissions()
        {
            // Ki<PERSON>m tra quyền của người dùng
            bool isAdmin = currentUser.VaiTro == VaiTroTaiKhoan.Admin;

            // Chỉ Admin mới được thêm/sửa/xóa - ẨN hoàn toàn cho User
            btnThem.Visible = isAdmin;
            btnSua.Visible = isAdmin;
            btnXoa.Visible = isAdmin;

            // User và Admin đều được xem chi tiết và xuất file
            btnXemChiTiet.Visible = true;
            btnXuatWord.Visible = true;
            btnXuatExcel.Visible = true;
            btnLamMoi.Visible = true;
        }

        private void SetupDataGridView()
        {
            dgvDeTai.Columns.Clear();
            dgvDeTai.AutoGenerateColumns = false;
            dgvDeTai.AllowUserToAddRows = false;
            dgvDeTai.AllowUserToDeleteRows = false;
            dgvDeTai.ReadOnly = true;
            dgvDeTai.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvDeTai.MultiSelect = false;

            // Cột STT - không có DataPropertyName, sẽ thêm thủ công
            dgvDeTai.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "STT",
                HeaderText = "STT",
                Width = 35,
                ReadOnly = true
            });

            dgvDeTai.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "MaDeTai",
                HeaderText = "Mã đề tài",
                DataPropertyName = "MaDeTai",
                Width = 100
            });

            dgvDeTai.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TenDeTai",
                HeaderText = "Tên đề tài",
                DataPropertyName = "TenDeTai",
                Width = 300
            });

            dgvDeTai.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LinhVuc",
                HeaderText = "Lĩnh vực",
                DataPropertyName = "LinhVuc",
                Width = 150
            });

            dgvDeTai.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CapQuanLy",
                HeaderText = "Cấp quản lý",
                DataPropertyName = "CapQuanLy",
                Width = 120
            });

            dgvDeTai.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ThoiGianBatDau",
                HeaderText = "Thời gian bắt đầu",
                DataPropertyName = "ThoiGianBatDau",
                Width = 120
            });

            dgvDeTai.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ChuNhiem",
                HeaderText = "Chủ nhiệm",
                DataPropertyName = "ChuNhiem",
                Width = 150,
                ReadOnly = true
            });
        }

        private async void LoadData()
        {
            try
            {
                allDeTai = await ExecuteDbOperationAsync(async context =>
                {
                    return await context.DeTai.ToListAsync();
                }, AppConstants.Messages.ErrorLoadingData);

                // Debug: Hiển thị số lượng bản ghi đã load
                lblTongSo.Text = $"Đã load: {allDeTai.Count} đề tài từ database";

                ApplyFilter();
            }
            catch (Exception ex)
            {
                ShowErrorMessage(AppConstants.Messages.ErrorLoadingData, ex);
            }
        }



        private async void LoadFilterData()
        {
            try
            {
                var linhVucList = await ExecuteDbOperationAsync(async context =>
                {
                    return await context.DeTai.Select(dt => dt.LinhVuc).Distinct().ToListAsync();
                }, "Lỗi khi tải dữ liệu lĩnh vực");

                // Load dữ liệu cho ComboBox Lĩnh vực
                cmbLinhVuc.Items.Clear();
                cmbLinhVuc.Items.Add("-- Tất cả --");
                foreach (var lv in linhVucList.Where(x => !string.IsNullOrEmpty(x)))
                {
                    cmbLinhVuc.Items.Add(lv ?? string.Empty);
                }
                cmbLinhVuc.SelectedIndex = 0;

                // Load dữ liệu cho ComboBox Cấp quản lý
                cmbCapQuanLy.Items.Clear();
                cmbCapQuanLy.Items.Add("-- Tất cả --");
                cmbCapQuanLy.Items.Add("Nhà nước");
                cmbCapQuanLy.Items.Add("Bộ");
                cmbCapQuanLy.Items.Add("Ngành");
                cmbCapQuanLy.Items.Add("Cơ sở");
                cmbCapQuanLy.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                ShowErrorMessage("Lỗi khi tải dữ liệu bộ lọc", ex);
            }
        }

        private async void ApplyFilter()
        {
            var filteredData = allDeTai.AsEnumerable();

            // Lọc theo tìm kiếm
            if (!string.IsNullOrEmpty(txtTimKiem.Text))
            {
                string searchText = txtTimKiem.Text.ToLower();
                filteredData = filteredData.Where(dt =>
                    (dt.TenDeTai?.ToLower().Contains(searchText) ?? false) ||
                    $"DT{dt.MaDeTai:D6}".ToLower().Contains(searchText));
            }

            // Lọc theo lĩnh vực
            if (cmbLinhVuc.SelectedIndex > 0 && cmbLinhVuc.SelectedItem != null)
            {
                string selectedLinhVuc = cmbLinhVuc.SelectedItem.ToString() ?? string.Empty;
                filteredData = filteredData.Where(dt => dt.LinhVuc == selectedLinhVuc);
            }

            // Lọc theo cấp quản lý
            if (cmbCapQuanLy.SelectedIndex > 0 && cmbCapQuanLy.SelectedItem != null)
            {
                string selectedCapQuanLy = cmbCapQuanLy.SelectedItem.ToString() ?? string.Empty;
                string capQuanLyEnum = selectedCapQuanLy switch
                {
                    "Nhà nước" => "NhaNuoc",
                    "Bộ" => "Bo",
                    "Ngành" => "Nganh",
                    "Cơ sở" => "CoSo",
                    _ => string.Empty
                };
                filteredData = filteredData.Where(dt => dt.CapQuanLy.ToString() == capQuanLyEnum);
            }

            // Lọc theo thời gian - chỉ lọc khi có giá trị thời gian bắt đầu
            filteredData = filteredData.Where(dt =>
                !dt.ThoiGianBatDau.HasValue ||
                (dt.ThoiGianBatDau.Value.Date >= dtpTuNgay.Value.Date &&
                 dt.ThoiGianBatDau.Value.Date <= dtpDenNgay.Value.Date));

            var filteredList = filteredData.ToList();

            // Clear existing rows
            dgvDeTai.Rows.Clear();

            try
            {
                // Lấy tất cả mã đề tài cần query
                var deTaiIds = filteredList.Select(dt => dt.MaDeTai).ToList();

                // Query một lần để lấy tất cả thông tin chủ nhiệm using DbService
                var chuNhiemDict = await ExecuteDbOperationAsync(async context =>
                {
                    return await context.VaiTroThamGia
                        .Where(vt => deTaiIds.Contains(vt.MaDeTai) && vt.VaiTro == VaiTroThamGiaEnum.ChuNhiem)
                        .Join(context.CanBo, vt => vt.MaCanBo, cb => cb.MaCanBo, (vt, cb) => new { vt.MaDeTai, cb.HoTen })
                        .ToDictionaryAsync(x => x.MaDeTai, x => x.HoTen);
                }, "Lỗi khi tải thông tin chủ nhiệm");

                // Debug: Kiểm tra số lượng chủ nhiệm tìm được
                System.Diagnostics.Debug.WriteLine($"Tìm được {chuNhiemDict.Count} chủ nhiệm cho {deTaiIds.Count} đề tài");

                // Thêm từng dòng vào DataGridView
                int stt = 1;
                foreach (var deTai in filteredList)
                {
                    var chuNhiem = chuNhiemDict.TryGetValue(deTai.MaDeTai, out string? tenChuNhiem) ? tenChuNhiem : "Chưa có";

                    dgvDeTai.Rows.Add(
                        stt++,
                        $"DT{deTai.MaDeTai:D6}",
                        deTai.TenDeTai,
                        deTai.LinhVuc,
                        GetCapQuanLyDisplayText(deTai.CapQuanLy),
                        deTai.ThoiGianBatDau?.ToString(AppConstants.Formats.DateFormat) ?? string.Empty,
                        chuNhiem
                    );
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("Lỗi khi tải thông tin chủ nhiệm", ex);
                // Fallback: hiển thị không có thông tin chủ nhiệm
                int stt = 1;
                foreach (var deTai in filteredList)
                {
                    dgvDeTai.Rows.Add(
                        stt++,
                        $"DT{deTai.MaDeTai:D6}",
                        deTai.TenDeTai,
                        deTai.LinhVuc,
                        GetCapQuanLyDisplayText(deTai.CapQuanLy),
                        deTai.ThoiGianBatDau?.ToString(AppConstants.Formats.DateFormat) ?? string.Empty,
                        "Chưa có"
                    );
                }
            }

            lblTongSo.Text = $"Tổng số: {filteredList.Count} đề tài";
        }

        private string GetCapQuanLyDisplayText(CapQuanLy capQuanLy)
        {
            return capQuanLy switch
            {
                CapQuanLy.NhaNuoc => "Nhà nước",
                CapQuanLy.Bo => "Bộ",
                CapQuanLy.Nganh => "Ngành",
                CapQuanLy.CoSo => "Cơ sở",
                _ => string.Empty
            };
        }

        #region Event Handlers - Filter
        private void txtTimKiem_TextChanged(object sender, EventArgs e)
        {
            ApplyFilter();
        }

        private void cmbLinhVuc_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilter();
        }

        private void cmbCapQuanLy_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilter();
        }

        private void dtpTuNgay_ValueChanged(object sender, EventArgs e)
        {
            ApplyFilter();
        }

        private void dtpDenNgay_ValueChanged(object sender, EventArgs e)
        {
            ApplyFilter();
        }

        private void btnLamMoi_Click(object sender, EventArgs e)
        {
            dgvDeTai.DataSource = null;
            dgvDeTai.ClearSelection();

            txtTimKiem.Text = string.Empty;
            cmbLinhVuc.SelectedIndex = 0;
            cmbCapQuanLy.SelectedIndex = 0;
            dtpTuNgay.Value = DateTime.Now.AddYears(-5);  // Mở rộng phạm vi để hiển thị tất cả
            dtpDenNgay.Value = DateTime.Now.AddYears(5);
            LoadData();
        }
        #endregion

        #region Event Handlers - CRUD
        private void btnThem_Click(object sender, EventArgs e)
        {
            if (!CheckAdminPermission()) return;

            var frmThem = new frmThemDeTai();
            if (frmThem.ShowDialog() == DialogResult.OK)
            {
                LoadData();
                ShowSuccessMessage(AppConstants.Messages.AddSuccess);
            }
        }

        private void btnSua_Click(object sender, EventArgs e)
        {
            if (!CheckAdminPermission()) return;

            if (dgvDeTai.SelectedRows.Count == 0)
            {
                ShowWarningMessage(AppConstants.Messages.WarningSelectItem);
                return;
            }

            // Lấy mã đề tài từ cột MaDeTai
            var maDeTaiStr = dgvDeTai.SelectedRows[0].Cells["MaDeTai"].Value?.ToString();
            if (!string.IsNullOrEmpty(maDeTaiStr))
            {
                // Chuyển đổi từ "DT000001" thành 1
                var maDeTaiNumber = maDeTaiStr.Replace("DT", string.Empty);
                if (int.TryParse(maDeTaiNumber, out int maDeTai))
                {
                    var frmSua = new frmThemDeTai(maDeTai);
                    if (frmSua.ShowDialog() == DialogResult.OK)
                    {
                        LoadData();
                        //ShowSuccessMessage(AppConstants.Messages.UpdateSuccess);
                    }
                }
            }
        }

        private async void btnXoa_Click(object sender, EventArgs e)
        {
            if (!CheckAdminPermission()) return;

            if (dgvDeTai.SelectedRows.Count == 0)
            {
                ShowWarningMessage(AppConstants.Messages.WarningSelectItem);
                return;
            }

            // Lấy mã đề tài và tên đề tài từ DataGridView
            var maDeTaiStr = dgvDeTai.SelectedRows[0].Cells["MaDeTai"].Value?.ToString();
            var tenDeTai = dgvDeTai.SelectedRows[0].Cells["TenDeTai"].Value?.ToString();

            if (!string.IsNullOrEmpty(maDeTaiStr))
            {
                // Chuyển đổi từ "DT000001" thành 1
                var maDeTaiNumber = maDeTaiStr.Replace("DT", string.Empty);
                if (int.TryParse(maDeTaiNumber, out int maDeTai))
                {
                    if (!AskConfirmation($"Bạn có chắc chắn muốn xóa đề tài '{tenDeTai}'?\nHành động này không thể hoàn tác!", "Xác nhận xóa"))
                        return;

                    try
                    {
                        await ExecuteDbOperationAsync(async context =>
                        {
                            var deTai = await context.DeTai.FindAsync(maDeTai);
                            if (deTai != null)
                            {
                                context.DeTai.Remove(deTai);
                                await context.SaveChangesAsync();
                            }
                        }, AppConstants.Messages.ErrorDeletingData);

                        LoadData();
                        //ShowSuccessMessage(AppConstants.Messages.DeleteSuccess);
                    }
                    catch (Exception ex)
                    {
                        ShowErrorMessage(AppConstants.Messages.ErrorDeletingData, ex);
                    }
                }
            }
        }

        private void btnXemChiTiet_Click(object sender, EventArgs e)
        {
            if (dgvDeTai.SelectedRows.Count == 0)
            {
                ShowWarningMessage(AppConstants.Messages.WarningSelectItem);
                return;
            }

            // Lấy mã đề tài từ cột MaDeTai
            var maDeTaiStr = dgvDeTai.SelectedRows[0].Cells["MaDeTai"].Value?.ToString();
            if (!string.IsNullOrEmpty(maDeTaiStr))
            {
                // Chuyển đổi từ "DT000001" thành 1
                var maDeTaiNumber = maDeTaiStr.Replace("DT", string.Empty);
                if (int.TryParse(maDeTaiNumber, out int maDeTai))
                {
                    LoadDeTaiChiTietForm(maDeTai);
                }
            }
        }

        private void LoadDeTaiChiTietForm(int maDeTai)
        {
            // Tìm panel cha (panelMain trong MainForm)
            var parentPanel = FindParentPanel();
            if (parentPanel != null)
            {
                // Xóa nội dung hiện tại trong panel
                parentPanel.Controls.Clear();

                // Tạo form chi tiết với callback để quay lại
                var frmChiTiet = new frmDeTaiChiTiet(currentUser, maDeTai, () => LoadBackToDeTaiForm(parentPanel))
                {
                    TopLevel = false,
                    FormBorderStyle = FormBorderStyle.None,
                    Dock = DockStyle.Fill
                };

                // Thêm form vào panel
                parentPanel.Controls.Add(frmChiTiet);
                frmChiTiet.Show();
            }
            else
            {
                // Fallback: mở dialog như cũ
                var frmChiTiet = new frmDeTaiChiTiet(currentUser, maDeTai);
                frmChiTiet.ShowDialog();
            }
        }

        private Panel? FindParentPanel()
        {
            // Tìm panel cha (panelMain) từ MainForm
            Control? parent = this.Parent;
            while (parent != null)
            {
                if (parent is Panel panel && panel.Name == "panelMain")
                {
                    return panel;
                }
                parent = parent.Parent;
            }
            return null;
        }

        private void LoadBackToDeTaiForm(Panel parentPanel)
        {
            // Xóa nội dung hiện tại trong panel
            parentPanel.Controls.Clear();

            // Tạo lại form DeTai
            var deTaiForm = new frmDeTai(currentUser)
            {
                TopLevel = false,
                FormBorderStyle = FormBorderStyle.None,
                Dock = DockStyle.Fill
            };

            // Thêm form vào panel
            parentPanel.Controls.Add(deTaiForm);
            deTaiForm.Show();
        }

        private void dgvDeTai_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnXemChiTiet_Click(sender, e);
            }
        }

        private void btnXuatWord_Click(object sender, EventArgs e)
        {
            try
            {
                ShowWarningMessage("Chức năng xuất Word đang được phát triển!");
                // TODO: Implement Word export functionality
            }
            catch (Exception ex)
            {
                ShowErrorMessage("Lỗi khi xuất Word", ex);
            }
        }

        private void btnXuatExcel_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvDeTai.Rows.Count == 0)
                {
                    ShowWarningMessage(AppConstants.Messages.InfoNoDataFound);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = AppConstants.Files.ExcelFilter,
                    Title = "Lưu file Excel",
                    FileName = $"DanhSachDeTai_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToExcel(saveFileDialog.FileName);
                    ShowSuccessMessage($"Xuất Excel thành công!\nFile đã được lưu tại: {saveFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage(AppConstants.Messages.ErrorExportingData, ex);
            }
        }

        private void ExportToExcel(string filePath)
        {
            using (var workbook = new ClosedXML.Excel.XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("Danh sách đề tài");

                // Tiêu đề
                worksheet.Cell(1, 1).Value = "DANH SÁCH ĐỀ TÀI NGHIÊN CỨU";
                worksheet.Range(1, 1, 1, 7).Merge().Style.Font.Bold = true;
                worksheet.Range(1, 1, 1, 7).Style.Font.FontSize = 16;
                worksheet.Range(1, 1, 1, 7).Style.Alignment.Horizontal = ClosedXML.Excel.XLAlignmentHorizontalValues.Center;

                // Thông tin xuất
                worksheet.Cell(2, 1).Value = $"Ngày xuất: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";
                worksheet.Cell(3, 1).Value = $"Người xuất: {currentUser.TenDangNhap}";

                // Header
                int headerRow = 5;
                worksheet.Cell(headerRow, 1).Value = "STT";
                worksheet.Cell(headerRow, 2).Value = "Mã đề tài";
                worksheet.Cell(headerRow, 3).Value = "Tên đề tài";
                worksheet.Cell(headerRow, 4).Value = "Lĩnh vực";
                worksheet.Cell(headerRow, 5).Value = "Cấp quản lý";
                worksheet.Cell(headerRow, 6).Value = "Thời gian bắt đầu";
                worksheet.Cell(headerRow, 7).Value = "Chủ nhiệm";

                // Style header
                var headerRange = worksheet.Range(headerRow, 1, headerRow, 7);
                headerRange.Style.Font.Bold = true;
                headerRange.Style.Fill.BackgroundColor = ClosedXML.Excel.XLColor.LightBlue;
                headerRange.Style.Border.OutsideBorder = ClosedXML.Excel.XLBorderStyleValues.Thick;
                headerRange.Style.Border.InsideBorder = ClosedXML.Excel.XLBorderStyleValues.Thin;

                // Data
                int currentRow = headerRow + 1;
                for (int i = 0; i < dgvDeTai.Rows.Count; i++)
                {
                    var row = dgvDeTai.Rows[i];
                    worksheet.Cell(currentRow, 1).Value = i + 1;
                    worksheet.Cell(currentRow, 2).Value = row.Cells["MaDeTai"].Value?.ToString() ?? string.Empty;
                    worksheet.Cell(currentRow, 3).Value = row.Cells["TenDeTai"].Value?.ToString() ?? string.Empty;
                    worksheet.Cell(currentRow, 4).Value = row.Cells["LinhVuc"].Value?.ToString() ?? string.Empty;
                    worksheet.Cell(currentRow, 5).Value = row.Cells["CapQuanLy"].Value?.ToString() ?? string.Empty;
                    worksheet.Cell(currentRow, 6).Value = row.Cells["ThoiGianBatDau"].Value?.ToString() ?? string.Empty;
                    worksheet.Cell(currentRow, 7).Value = row.Cells["ChuNhiem"].Value?.ToString() ?? string.Empty;
                    currentRow++;
                }

                // Style data
                var dataRange = worksheet.Range(headerRow + 1, 1, currentRow - 1, 7);
                dataRange.Style.Border.OutsideBorder = ClosedXML.Excel.XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = ClosedXML.Excel.XLBorderStyleValues.Thin;

                // Auto fit columns
                worksheet.Columns().AdjustToContents();

                // Tổng số
                worksheet.Cell(currentRow + 1, 1).Value = $"Tổng số: {dgvDeTai.Rows.Count} đề tài";
                worksheet.Range(currentRow + 1, 1, currentRow + 1, 7).Merge().Style.Font.Bold = true;

                workbook.SaveAs(filePath);
            }
        }
        #endregion

        #region Helper Methods
        private bool CheckAdminPermission()
        {
            if (currentUser.VaiTro != VaiTroTaiKhoan.Admin)
            {
                MessageBox.Show("Bạn không có quyền thực hiện chức năng này!\nChỉ Admin mới có thể thực hiện.",
                    "Không có quyền", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
            return true;
        }
        #endregion

        private void dgvDeTai_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {

        }
    }
}
